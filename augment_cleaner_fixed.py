#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 修复版
确保每一步都有明确的输出和错误处理
"""

import os
import sys
import json
import sqlite3
import platform
import shutil
import random
import string
import uuid
import time
from pathlib import Path

def main():
    print("=" * 60)
    print("🔧 Augment续杯工具 v1.0.0")
    print("=" * 60)
    
    try:
        # 步骤1: 检测系统
        print("\n📍 步骤1: 检测系统环境")
        system = platform.system()
        print(f"   操作系统: {system}")
        
        # 步骤2: 获取VS Code路径
        print("\n📁 步骤2: 检测VS Code路径")
        home = Path.home()
        print(f"   用户目录: {home}")
        
        if system == "Windows":
            base_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
        elif system == "Darwin":  # macOS
            base_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
        elif system == "Linux":
            base_path = home / ".config" / "Code" / "User" / "globalStorage"
        else:
            print(f"   ❌ 不支持的操作系统: {system}")
            input("按回车键退出...")
            return
        
        print(f"   VS Code配置目录: {base_path}")
        
        storage_json = base_path / "storage.json"
        state_db = base_path / "state.vscdb"
        
        print(f"   storage.json: {'✅存在' if storage_json.exists() else '❌不存在'}")
        print(f"   state.vscdb: {'✅存在' if state_db.exists() else '❌不存在'}")
        
        # 步骤3: 用户确认
        print("\n⚠️ 步骤3: 用户确认")
        print("此操作将:")
        print("   1. 备份原始文件")
        print("   2. 修改机器码和设备码")
        print("   3. 清理Augment相关数据")
        
        while True:
            try:
                confirm = input("\n是否继续? 输入 y 继续，n 退出: ").strip().lower()
                print(f"   您输入了: '{confirm}'")
                
                if confirm == 'y':
                    print("   ✅ 用户确认继续")
                    break
                elif confirm == 'n':
                    print("   ❌ 用户取消操作")
                    input("按回车键退出...")
                    return
                else:
                    print("   请输入 y 或 n")
                    continue
                    
            except KeyboardInterrupt:
                print("\n   ❌ 用户中断操作")
                return
            except Exception as e:
                print(f"   ❌ 输入错误: {e}")
                return
        
        # 步骤4: 创建备份
        print("\n💾 步骤4: 备份原始文件")
        try:
            backup_dir = Path("./backups")
            backup_dir.mkdir(exist_ok=True)
            print(f"   备份目录: {backup_dir}")
            
            timestamp = str(int(time.time()))
            backup_folder = backup_dir / f"backup_{timestamp}"
            backup_folder.mkdir(exist_ok=True)
            print(f"   本次备份: {backup_folder}")
            
            backup_count = 0
            if storage_json.exists():
                backup_path = backup_folder / f"storage.json_{timestamp}"
                shutil.copy2(storage_json, backup_path)
                print(f"   ✅ 已备份: storage.json")
                backup_count += 1
            
            if state_db.exists():
                backup_path = backup_folder / f"state.vscdb_{timestamp}"
                shutil.copy2(state_db, backup_path)
                print(f"   ✅ 已备份: state.vscdb")
                backup_count += 1
            
            print(f"   📊 共备份 {backup_count} 个文件")
            
        except Exception as e:
            print(f"   ❌ 备份失败: {e}")
            input("按回车键退出...")
            return
        
        # 步骤5: 修改storage.json
        print("\n🔧 步骤5: 修改机器码")
        try:
            if storage_json.exists():
                with open(storage_json, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"   ✅ 读取storage.json成功")
                
                modified = False
                if "telemetry.machineId" in data:
                    old_id = data["telemetry.machineId"]
                    data["telemetry.machineId"] = str(uuid.uuid4())
                    print(f"   ✅ 修改机器ID: {old_id[:8]}... -> {data['telemetry.machineId'][:8]}...")
                    modified = True
                
                if "telemetry.devDeviceId" in data:
                    old_id = data["telemetry.devDeviceId"]
                    data["telemetry.devDeviceId"] = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
                    print(f"   ✅ 修改设备ID: {old_id[:8]}... -> {data['telemetry.devDeviceId'][:8]}...")
                    modified = True
                
                if modified:
                    with open(storage_json, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2, ensure_ascii=False)
                    print(f"   ✅ storage.json修改完成")
                else:
                    print(f"   ⚠️ 未找到需要修改的字段")
            else:
                print(f"   ⚠️ storage.json不存在，跳过修改")
                
        except Exception as e:
            print(f"   ❌ 修改storage.json失败: {e}")
            input("按回车键退出...")
            return
        
        # 步骤6: 清理数据库
        print("\n🧹 步骤6: 清理数据库")
        try:
            if state_db.exists():
                conn = sqlite3.connect(str(state_db))
                cursor = conn.cursor()
                print(f"   ✅ 连接数据库成功")
                
                # 查找记录
                cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%'")
                records = cursor.fetchall()
                print(f"   🔍 找到 {len(records)} 条Augment记录")
                
                if records:
                    cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
                    conn.commit()
                    deleted_count = cursor.rowcount
                    print(f"   ✅ 删除了 {deleted_count} 条记录")
                else:
                    print(f"   ✅ 没有需要清理的记录")
                
                conn.close()
            else:
                print(f"   ⚠️ state.vscdb不存在，跳过清理")
                
        except Exception as e:
            print(f"   ❌ 清理数据库失败: {e}")
            input("按回车键退出...")
            return
        
        # 完成
        print("\n" + "=" * 60)
        print("🎉 Augment续杯清理完成!")
        print("=" * 60)
        print("📝 后续步骤:")
        print("   1. 重新启动VS Code")
        print("   2. 使用新邮箱注册Augment账号")
        print("   3. 开始使用新的免费额度")
        print("=" * 60)
        
        input("\n按回车键退出...")
        
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
