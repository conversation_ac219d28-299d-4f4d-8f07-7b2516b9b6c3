#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 安装脚本
自动检查环境并安装依赖
"""

import sys
import subprocess
import platform
import os

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_and_install_dependencies():
    """检查并安装依赖"""
    print("📦 检查依赖包...")
    
    dependencies = ["psutil"]
    
    for package in dependencies:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📥 正在安装 {package}...")
            if install_package(package):
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败")
                return False
    
    return True

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if platform.system() != "Windows":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Augment续杯工具.lnk")
        target = os.path.join(os.getcwd(), "run_cleaner.bat")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = os.getcwd()
        shortcut.IconLocation = target
        shortcut.save()
        
        print("✅ 桌面快捷方式创建成功")
    except:
        print("⚠️ 桌面快捷方式创建失败（可选功能）")

def main():
    """主安装函数"""
    print("=" * 50)
    print("🔧 Augment续杯工具 - 安装程序")
    print("=" * 50)
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        sys.exit(1)
    
    # 安装依赖
    if not check_and_install_dependencies():
        print("❌ 依赖安装失败")
        input("按回车键退出...")
        sys.exit(1)
    
    # 创建必要目录
    os.makedirs("backups", exist_ok=True)
    print("✅ 创建备份目录")
    
    # 设置脚本权限（Linux/macOS）
    if platform.system() in ["Linux", "Darwin"]:
        try:
            os.chmod("run_cleaner.sh", 0o755)
            print("✅ 设置脚本执行权限")
        except:
            print("⚠️ 设置脚本权限失败")
    
    # 创建桌面快捷方式（Windows）
    if platform.system() == "Windows":
        create_desktop_shortcut()
    
    print()
    print("🎉 安装完成！")
    print()
    print("📋 使用方法:")
    if platform.system() == "Windows":
        print("   双击 run_cleaner.bat 启动工具")
    else:
        print("   运行 ./run_cleaner.sh 启动工具")
    
    print()
    print("📖 更多信息请查看 README.md")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
