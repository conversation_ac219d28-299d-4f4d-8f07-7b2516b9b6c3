#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 v1.0.0
功能: 自动清理VS Code中Augment插件的使用痕迹，实现续杯功能
使用: python augment_cleaner.py
"""

import os
import sys
import json
import sqlite3
import platform
import shutil
import random
import string
import uuid
import time
from pathlib import Path

class AugmentCleaner:
    """Augment续杯清理器"""

    def __init__(self):
        self.system = platform.system()
        self.vscode_paths = self._get_vscode_paths()
        self.backup_dir = Path("./backups")
        self.backup_dir.mkdir(exist_ok=True)

    def _get_vscode_paths(self):
        """获取不同系统下VS Code的配置路径"""
        home = Path.home()

        if self.system == "Windows":
            base_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
        elif self.system == "Darwin":  # macOS
            base_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
        elif self.system == "Linux":
            base_path = home / ".config" / "Code" / "User" / "globalStorage"
        else:
            raise OSError(f"不支持的操作系统: {self.system}")

        return {
            "storage_json": base_path / "storage.json",
            "state_db": base_path / "state.vscdb",
            "base_path": base_path
        }

    def _generate_new_machine_id(self):
        """生成新的机器ID"""
        return str(uuid.uuid4())

    def _generate_new_device_id(self):
        """生成新的设备ID"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
    
    def backup_files(self):
        """备份原始文件"""
        try:
            timestamp = str(int(time.time()))
            backup_folder = self.backup_dir / f"backup_{timestamp}"
            backup_folder.mkdir(exist_ok=True)

            for name, path in self.vscode_paths.items():
                if path.exists() and path.is_file():
                    backup_path = backup_folder / f"{name}_{timestamp}"
                    shutil.copy2(path, backup_path)
                    print(f"✅ 已备份: {path.name}")

            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False

    def modify_storage_json(self):
        """修改storage.json中的机器码"""
        storage_path = self.vscode_paths["storage_json"]

        try:
            if not storage_path.exists():
                print("⚠️ storage.json不存在，跳过修改")
                return True

            # 读取原始文件
            with open(storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 修改机器码和设备码
            if "telemetry.machineId" in data:
                old_id = data["telemetry.machineId"]
                data["telemetry.machineId"] = self._generate_new_machine_id()
                print(f"✅ 已修改机器ID: {old_id[:8]}... -> {data['telemetry.machineId'][:8]}...")

            if "telemetry.devDeviceId" in data:
                old_id = data["telemetry.devDeviceId"]
                data["telemetry.devDeviceId"] = self._generate_new_device_id()
                print(f"✅ 已修改设备ID: {old_id[:8]}... -> {data['telemetry.devDeviceId'][:8]}...")

            # 写回文件
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print("✅ storage.json修改完成")
            return True

        except Exception as e:
            print(f"❌ 修改storage.json失败: {e}")
            return False
    
    def clean_database(self):
        """清理state.vscdb数据库中的Augment相关数据"""
        db_path = self.vscode_paths["state_db"]

        try:
            if not db_path.exists():
                print("⚠️ 数据库不存在，跳过清理")
                return True

            # 连接数据库
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # 查找Augment相关的记录
            cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%'")
            records = cursor.fetchall()

            if records:
                print(f"🔍 找到 {len(records)} 条Augment相关记录")

                # 删除记录
                cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
                conn.commit()

                print(f"✅ 已删除 {cursor.rowcount} 条记录")
            else:
                print("✅ 未找到Augment相关记录")

            conn.close()
            return True

        except Exception as e:
            print(f"❌ 清理数据库失败: {e}")
            return False

    def check_vscode_running(self):
        """检查VS Code是否正在运行"""
        try:
            if self.system == "Windows":
                try:
                    import psutil
                    for proc in psutil.process_iter(['pid', 'name']):
                        if 'code' in proc.info['name'].lower():
                            return True
                except ImportError:
                    pass
            else:
                import subprocess
                result = subprocess.run(['pgrep', '-f', 'code'], capture_output=True)
                return result.returncode == 0
        except:
            pass
        return False
    
    def clean_all(self):
        """执行完整的清理流程"""
        print("🚀 开始Augment续杯清理流程...")

        # 检查VS Code是否运行
        if self.check_vscode_running():
            print("⚠️ 检测到VS Code正在运行，请先关闭VS Code后再执行清理")
            return False

        # 备份文件
        print("\n💾 正在备份原始文件...")
        if not self.backup_files():
            print("❌ 备份失败，停止清理流程")
            return False

        # 修改storage.json
        print("\n🔧 正在修改机器码...")
        if not self.modify_storage_json():
            print("❌ 修改机器码失败")
            return False

        # 清理数据库
        print("\n🧹 正在清理数据库...")
        if not self.clean_database():
            print("❌ 清理数据库失败")
            return False

        print("\n🎉 Augment续杯清理完成！")
        print("📝 请重新启动VS Code并使用新邮箱注册Augment账号")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 Augment续杯工具 v1.0.0")
    print("=" * 60)

    try:
        cleaner = AugmentCleaner()

        # 显示检测到的路径
        print(f"\n📍 检测到的系统: {cleaner.system}")
        print("📁 VS Code配置路径:")
        for name, path in cleaner.vscode_paths.items():
            if name == "base_path":
                continue
            status = "✅" if path.exists() else "❌"
            print(f"   {status} {name}: {path}")

        # 确认执行
        print("\n⚠️  警告: 此操作将修改VS Code配置文件")
        print("📋 操作内容:")
        print("   1. 备份原始文件")
        print("   2. 修改机器码和设备码")
        print("   3. 清理Augment相关数据")

        confirm = input("\n是否继续执行? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 操作已取消")
            return

        # 执行清理
        success = cleaner.clean_all()

        if success:
            print("\n" + "=" * 60)
            print("🎉 清理成功完成!")
            print("📝 后续步骤:")
            print("   1. 重新启动VS Code")
            print("   2. 使用新邮箱注册Augment账号")
            print("   3. 开始使用新的免费额度")
            print("=" * 60)
        else:
            print("\n❌ 清理过程中出现错误")

    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
