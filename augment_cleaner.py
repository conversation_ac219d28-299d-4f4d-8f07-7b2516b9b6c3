#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - VS Code环境清理器
作者: AI Assistant
版本: 1.0.0
功能: 自动清理VS Code中Augment插件的使用痕迹，实现续杯功能
"""

import os
import sys
import json
import sqlite3
import platform
import shutil
import random
import string
import uuid
import time
from pathlib import Path
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('augment_cleaner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AugmentCleaner:
    """Augment续杯清理器主类"""
    
    def __init__(self):
        self.system = platform.system()
        self.vscode_paths = self._get_vscode_paths()
        self.backup_dir = Path("./backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def _get_vscode_paths(self) -> Dict[str, Path]:
        """获取不同系统下VS Code的配置路径"""
        home = Path.home()
        
        if self.system == "Windows":
            base_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
        elif self.system == "Darwin":  # macOS
            base_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
        elif self.system == "Linux":
            base_path = home / ".config" / "Code" / "User" / "globalStorage"
        else:
            raise OSError(f"不支持的操作系统: {self.system}")
            
        return {
            "storage_json": base_path / "storage.json",
            "state_db": base_path / "state.vscdb",
            "base_path": base_path
        }
    
    def _generate_new_machine_id(self) -> str:
        """生成新的机器ID"""
        return str(uuid.uuid4())
    
    def _generate_new_device_id(self) -> str:
        """生成新的设备ID"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
    
    def backup_files(self) -> bool:
        """备份原始文件"""
        try:
            timestamp = str(int(time.time()))
            backup_folder = self.backup_dir / f"backup_{timestamp}"
            backup_folder.mkdir(exist_ok=True)
            
            for name, path in self.vscode_paths.items():
                if path.exists() and path.is_file():
                    backup_path = backup_folder / f"{name}_{timestamp}"
                    shutil.copy2(path, backup_path)
                    logger.info(f"已备份: {path} -> {backup_path}")
            
            return True
        except Exception as e:
            logger.error(f"备份失败: {e}")
            return False
    
    def modify_storage_json(self) -> bool:
        """修改storage.json中的机器码"""
        storage_path = self.vscode_paths["storage_json"]
        
        try:
            if not storage_path.exists():
                logger.warning(f"storage.json不存在: {storage_path}")
                return True
            
            # 读取原始文件
            with open(storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 修改机器码和设备码
            if "telemetry.machineId" in data:
                old_machine_id = data["telemetry.machineId"]
                data["telemetry.machineId"] = self._generate_new_machine_id()
                logger.info(f"已修改机器ID: {old_machine_id} -> {data['telemetry.machineId']}")
            
            if "telemetry.devDeviceId" in data:
                old_device_id = data["telemetry.devDeviceId"]
                data["telemetry.devDeviceId"] = self._generate_new_device_id()
                logger.info(f"已修改设备ID: {old_device_id} -> {data['telemetry.devDeviceId']}")
            
            # 写回文件
            with open(storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info("storage.json修改完成")
            return True
            
        except Exception as e:
            logger.error(f"修改storage.json失败: {e}")
            return False
    
    def clean_database(self) -> bool:
        """清理state.vscdb数据库中的Augment相关数据"""
        db_path = self.vscode_paths["state_db"]
        
        try:
            if not db_path.exists():
                logger.warning(f"数据库不存在: {db_path}")
                return True
            
            # 连接数据库
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # 查找Augment相关的记录
            cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%'")
            records = cursor.fetchall()
            
            if records:
                logger.info(f"找到 {len(records)} 条Augment相关记录")
                
                # 删除记录
                cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
                conn.commit()
                
                logger.info(f"已删除 {cursor.rowcount} 条记录")
            else:
                logger.info("未找到Augment相关记录")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"清理数据库失败: {e}")
            return False
    
    def check_vscode_running(self) -> bool:
        """检查VS Code是否正在运行"""
        try:
            if self.system == "Windows":
                import psutil
                for proc in psutil.process_iter(['pid', 'name']):
                    if 'code' in proc.info['name'].lower():
                        return True
            else:
                import subprocess
                result = subprocess.run(['pgrep', '-f', 'code'], capture_output=True)
                return result.returncode == 0
        except:
            pass
        return False
    
    def clean_all(self) -> bool:
        """执行完整的清理流程"""
        logger.info("开始Augment续杯清理流程...")
        
        # 检查VS Code是否运行
        if self.check_vscode_running():
            logger.warning("检测到VS Code正在运行，请先关闭VS Code后再执行清理")
            return False
        
        # 备份文件
        logger.info("正在备份原始文件...")
        if not self.backup_files():
            logger.error("备份失败，停止清理流程")
            return False
        
        # 修改storage.json
        logger.info("正在修改机器码...")
        if not self.modify_storage_json():
            logger.error("修改机器码失败")
            return False
        
        # 清理数据库
        logger.info("正在清理数据库...")
        if not self.clean_database():
            logger.error("清理数据库失败")
            return False
        
        logger.info("✅ Augment续杯清理完成！")
        logger.info("请重新启动VS Code并使用新邮箱注册Augment账号")
        return True

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 Augment续杯工具 v1.0.0")
    print("=" * 50)
    
    try:
        cleaner = AugmentCleaner()
        
        # 显示检测到的路径
        print(f"\n📍 检测到的系统: {cleaner.system}")
        print("📁 VS Code配置路径:")
        for name, path in cleaner.vscode_paths.items():
            status = "✅" if path.exists() else "❌"
            print(f"   {status} {name}: {path}")
        
        # 确认执行
        print("\n⚠️  警告: 此操作将修改VS Code配置文件")
        print("📋 操作内容:")
        print("   1. 备份原始文件")
        print("   2. 修改机器码和设备码")
        print("   3. 清理Augment相关数据")
        
        confirm = input("\n是否继续执行? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 操作已取消")
            return
        
        # 执行清理
        success = cleaner.clean_all()
        
        if success:
            print("\n🎉 清理成功完成!")
            print("📝 后续步骤:")
            print("   1. 重新启动VS Code")
            print("   2. 使用新邮箱注册Augment账号")
            print("   3. 开始使用新的免费额度")
        else:
            print("\n❌ 清理过程中出现错误，请查看日志文件")
            
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    import time
    main()
