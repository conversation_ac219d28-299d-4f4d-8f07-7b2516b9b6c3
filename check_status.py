#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 状态检查
快速检查当前环境和VS Code配置状态
"""

import os
import sys
import json
import sqlite3
import platform
from pathlib import Path

def check_python_environment():
    """检查Python环境"""
    print("🐍 Python环境检查:")
    print(f"   版本: {sys.version}")
    print(f"   可执行文件: {sys.executable}")
    
    # 检查必要模块
    modules = ['json', 'sqlite3', 'platform', 'pathlib', 'uuid', 'random', 'string']
    missing_modules = []
    
    for module in modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"   ❌ 缺少模块: {', '.join(missing_modules)}")
        return False
    else:
        print("   ✅ 基础模块完整")
    
    # 检查psutil
    try:
        import psutil
        print("   ✅ psutil模块可用")
    except ImportError:
        print("   ⚠️ psutil模块未安装（可选）")
    
    return True

def check_vscode_paths():
    """检查VS Code路径"""
    print("\n📁 VS Code路径检查:")
    
    system = platform.system()
    home = Path.home()
    
    print(f"   操作系统: {system}")
    print(f"   用户目录: {home}")
    
    if system == "Windows":
        base_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
    elif system == "Darwin":  # macOS
        base_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
    elif system == "Linux":
        base_path = home / ".config" / "Code" / "User" / "globalStorage"
    else:
        print(f"   ❌ 不支持的操作系统: {system}")
        return False
    
    print(f"   配置目录: {base_path}")
    
    if not base_path.exists():
        print("   ❌ VS Code配置目录不存在")
        print("   💡 请确认VS Code已安装并至少运行过一次")
        return False
    
    print("   ✅ VS Code配置目录存在")
    
    # 检查关键文件
    storage_json = base_path / "storage.json"
    state_db = base_path / "state.vscdb"
    
    print(f"   storage.json: {'✅ 存在' if storage_json.exists() else '❌ 不存在'}")
    print(f"   state.vscdb: {'✅ 存在' if state_db.exists() else '❌ 不存在'}")
    
    return True

def check_storage_json():
    """检查storage.json内容"""
    print("\n📄 storage.json检查:")
    
    system = platform.system()
    home = Path.home()
    
    if system == "Windows":
        base_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
    elif system == "Darwin":  # macOS
        base_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
    elif system == "Linux":
        base_path = home / ".config" / "Code" / "User" / "globalStorage"
    else:
        return False
    
    storage_file = base_path / "storage.json"
    
    if not storage_file.exists():
        print("   ❌ storage.json文件不存在")
        return False
    
    try:
        with open(storage_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("   ✅ 文件格式正确")
        
        # 检查关键字段
        machine_id = data.get("telemetry.machineId")
        device_id = data.get("telemetry.devDeviceId")
        
        if machine_id:
            print(f"   机器ID: {machine_id}")
        else:
            print("   ⚠️ 未找到机器ID字段")
        
        if device_id:
            print(f"   设备ID: {device_id}")
        else:
            print("   ⚠️ 未找到设备ID字段")
        
        print(f"   总字段数: {len(data)}")
        return True
        
    except json.JSONDecodeError:
        print("   ❌ JSON格式错误")
        return False
    except Exception as e:
        print(f"   ❌ 读取失败: {e}")
        return False

def check_database():
    """检查state.vscdb数据库"""
    print("\n🗄️ state.vscdb检查:")
    
    system = platform.system()
    home = Path.home()
    
    if system == "Windows":
        base_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
    elif system == "Darwin":  # macOS
        base_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
    elif system == "Linux":
        base_path = home / ".config" / "Code" / "User" / "globalStorage"
    else:
        return False
    
    db_file = base_path / "state.vscdb"
    
    if not db_file.exists():
        print("   ❌ state.vscdb文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"   ✅ 数据库可访问，包含 {len(tables)} 个表")
        
        # 检查ItemTable
        if ('ItemTable',) in tables:
            print("   ✅ ItemTable表存在")
            
            # 统计总记录数
            cursor.execute("SELECT COUNT(*) FROM ItemTable")
            total_count = cursor.fetchone()[0]
            print(f"   总记录数: {total_count}")
            
            # 统计Augment相关记录
            cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
            augment_count = cursor.fetchone()[0]
            print(f"   Augment相关记录: {augment_count}")
            
            if augment_count > 0:
                print("   💡 发现Augment使用痕迹，可以进行清理")
            else:
                print("   ✅ 未发现Augment使用痕迹")
        else:
            print("   ⚠️ ItemTable表不存在")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"   ❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 访问失败: {e}")
        return False

def check_vscode_running():
    """检查VS Code是否运行"""
    print("\n🔍 VS Code运行状态检查:")
    
    try:
        system = platform.system()
        
        if system == "Windows":
            try:
                import psutil
                running_processes = []
                for proc in psutil.process_iter(['pid', 'name']):
                    if 'code' in proc.info['name'].lower():
                        running_processes.append(proc.info['name'])
                
                if running_processes:
                    print("   ⚠️ 检测到VS Code正在运行:")
                    for proc_name in set(running_processes):
                        print(f"     - {proc_name}")
                    print("   💡 建议关闭VS Code后再执行清理")
                    return True
                else:
                    print("   ✅ VS Code未运行")
                    return False
            except ImportError:
                print("   ⚠️ 无法检测（psutil未安装）")
                return False
        else:
            import subprocess
            try:
                result = subprocess.run(['pgrep', '-f', 'code'], capture_output=True, text=True)
                if result.returncode == 0:
                    print("   ⚠️ 检测到VS Code正在运行")
                    print("   💡 建议关闭VS Code后再执行清理")
                    return True
                else:
                    print("   ✅ VS Code未运行")
                    return False
            except FileNotFoundError:
                print("   ⚠️ 无法检测（pgrep命令不可用）")
                return False
    except Exception as e:
        print(f"   ❌ 检测失败: {e}")
        return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 Augment续杯工具 - 状态检查")
    print("=" * 60)
    
    checks = [
        ("Python环境", check_python_environment),
        ("VS Code路径", check_vscode_paths),
        ("storage.json", check_storage_json),
        ("state.vscdb", check_database),
        ("VS Code运行状态", check_vscode_running)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"   ❌ 检查异常: {e}")
            results.append((check_name, False))
    
    # 显示检查结果汇总
    print("\n" + "=" * 60)
    print("📊 检查结果汇总")
    print("=" * 60)
    
    all_good = True
    for check_name, result in results:
        if result is True:
            status = "✅ 正常"
        elif result is False:
            status = "❌ 异常"
            all_good = False
        else:
            status = "⚠️ 警告"
    
        print(f"{check_name:20} : {status}")
    
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 环境检查通过！可以使用Augment续杯工具。")
        print("🚀 运行命令: python augment_cleaner_gui.py")
    else:
        print("⚠️ 发现一些问题，请根据上述提示进行处理。")
    print("=" * 60)

if __name__ == "__main__":
    main()
