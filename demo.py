#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 功能演示
展示工具的主要功能和使用方法
"""

import os
import sys
import json
import sqlite3
import tempfile
import shutil
from pathlib import Path
import platform

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n📋 步骤 {step}: {description}")
    print("-" * 40)

def demo_environment_detection():
    """演示环境检测功能"""
    print_header("环境检测演示")
    
    print("🔍 检测当前运行环境...")
    
    # 系统信息
    system = platform.system()
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    print(f"   操作系统: {system}")
    print(f"   Python版本: {python_version}")
    print(f"   工作目录: {os.getcwd()}")
    
    # VS Code路径检测
    home = Path.home()
    
    if system == "Windows":
        vscode_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
    elif system == "Darwin":  # macOS
        vscode_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
    elif system == "Linux":
        vscode_path = home / ".config" / "Code" / "User" / "globalStorage"
    else:
        vscode_path = None
    
    if vscode_path:
        print(f"\n📁 VS Code配置目录:")
        print(f"   路径: {vscode_path}")
        print(f"   存在: {'✅ 是' if vscode_path.exists() else '❌ 否'}")
        
        if vscode_path.exists():
            storage_json = vscode_path / "storage.json"
            state_db = vscode_path / "state.vscdb"
            print(f"   storage.json: {'✅ 存在' if storage_json.exists() else '❌ 不存在'}")
            print(f"   state.vscdb: {'✅ 存在' if state_db.exists() else '❌ 不存在'}")
    
    print("\n✅ 环境检测完成")

def demo_file_operations():
    """演示文件操作功能"""
    print_header("文件操作演示")
    
    # 创建临时演示环境
    temp_dir = Path(tempfile.mkdtemp(prefix="augment_demo_"))
    print(f"📁 创建演示目录: {temp_dir}")
    
    try:
        print_step(1, "创建模拟的storage.json文件")
        
        # 创建模拟的storage.json
        storage_data = {
            "telemetry.machineId": "demo-machine-id-12345",
            "telemetry.devDeviceId": "demo-device-id-67890",
            "other.setting": "some-other-value",
            "vscode.version": "1.85.0"
        }
        
        storage_file = temp_dir / "storage.json"
        with open(storage_file, 'w', encoding='utf-8') as f:
            json.dump(storage_data, f, indent=2, ensure_ascii=False)
        
        print("   ✅ storage.json创建成功")
        print(f"   原始机器ID: {storage_data['telemetry.machineId']}")
        print(f"   原始设备ID: {storage_data['telemetry.devDeviceId']}")
        
        print_step(2, "修改机器码和设备码")
        
        # 生成新的ID
        import uuid
        import random
        import string
        
        new_machine_id = str(uuid.uuid4())
        new_device_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
        
        # 读取并修改文件
        with open(storage_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        data["telemetry.machineId"] = new_machine_id
        data["telemetry.devDeviceId"] = new_device_id
        
        with open(storage_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 机器码修改成功")
        print(f"   新机器ID: {new_machine_id}")
        print(f"   新设备ID: {new_device_id}")
        
        print_step(3, "创建和清理模拟数据库")
        
        # 创建模拟数据库
        db_file = temp_dir / "state.vscdb"
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 创建表结构
        cursor.execute('''
            CREATE TABLE ItemTable (
                key TEXT PRIMARY KEY,
                value TEXT
            )
        ''')
        
        # 插入测试数据
        test_records = [
            ("augment.lastUsed", "2025-01-13"),
            ("augment.userSettings", '{"theme": "dark"}'),
            ("augmentcode.apiKey", "demo-api-key"),
            ("other.extension.setting", "other-value"),
            ("vscode.theme", "dark-plus")
        ]
        
        cursor.executemany("INSERT INTO ItemTable (key, value) VALUES (?, ?)", test_records)
        conn.commit()
        
        # 查询Augment相关记录
        cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment%'")
        augment_records = cursor.fetchall()
        
        print(f"   ✅ 数据库创建成功，包含 {len(augment_records)} 条Augment记录:")
        for key, value in augment_records:
            print(f"     - {key}: {value}")
        
        # 删除Augment相关记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        deleted_count = cursor.rowcount
        conn.commit()
        
        # 验证删除结果
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        remaining_count = cursor.fetchone()[0]
        
        print(f"   ✅ 清理完成，删除了 {deleted_count} 条记录")
        print(f"   剩余Augment记录: {remaining_count} 条")
        
        conn.close()
        
        print_step(4, "备份功能演示")
        
        # 创建备份目录
        backup_dir = temp_dir / "backups"
        backup_dir.mkdir()
        
        import time
        timestamp = str(int(time.time()))
        backup_folder = backup_dir / f"backup_{timestamp}"
        backup_folder.mkdir()
        
        # 备份文件
        files_to_backup = [storage_file, db_file]
        for file_path in files_to_backup:
            if file_path.exists():
                backup_path = backup_folder / f"{file_path.name}_{timestamp}"
                shutil.copy2(file_path, backup_path)
                print(f"   ✅ 已备份: {file_path.name} -> {backup_path.name}")
        
        print("\n🎉 文件操作演示完成")
        
    finally:
        # 清理演示环境
        print(f"\n🧽 清理演示环境: {temp_dir}")
        shutil.rmtree(temp_dir)

def demo_cleaner_usage():
    """演示清理器使用方法"""
    print_header("清理器使用演示")
    
    print("📖 Augment续杯工具使用流程:")
    
    steps = [
        "关闭VS Code（重要！）",
        "运行续杯工具",
        "工具自动检测环境",
        "备份原始文件",
        "修改机器码和设备码",
        "清理Augment相关数据",
        "重新启动VS Code",
        "使用新邮箱注册Augment账号",
        "开始使用新的免费额度"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"   {i}. {step}")
    
    print("\n💡 使用提示:")
    print("   • 图形界面版本: python augment_cleaner_gui.py")
    print("   • 命令行版本: python augment_cleaner.py")
    print("   • Windows快捷方式: run_cleaner.bat")
    print("   • Linux/macOS: ./run_cleaner.sh")
    
    print("\n⚠️ 注意事项:")
    print("   • 使用前必须完全关闭VS Code")
    print("   • 建议使用新邮箱注册新账号")
    print("   • 工具会自动备份，安全可靠")
    print("   • 支持Windows、macOS、Linux三大平台")

def demo_features():
    """演示工具特性"""
    print_header("工具特性展示")
    
    features = [
        ("🎯 自动清理", "一键清理Augment使用痕迹，重置免费额度"),
        ("💾 安全备份", "操作前自动备份原始文件，支持恢复"),
        ("🖥️ 跨平台", "支持Windows、macOS、Linux三大操作系统"),
        ("🎨 双界面", "提供图形界面和命令行两种使用模式"),
        ("🔍 智能检测", "自动检测VS Code运行状态和配置路径"),
        ("📝 详细日志", "完整记录所有操作，便于问题排查"),
        ("🛡️ 错误处理", "完善的异常处理和错误恢复机制"),
        ("⚡ 快速执行", "整个清理过程通常在几秒内完成")
    ]
    
    for feature, description in features:
        print(f"   {feature} {description}")
    
    print("\n🔧 技术实现:")
    print("   • 修改VS Code的telemetry.machineId和telemetry.devDeviceId")
    print("   • 清理state.vscdb数据库中的Augment相关记录")
    print("   • 使用UUID和随机字符串生成新的设备标识")
    print("   • 支持SQLite数据库操作和JSON文件处理")

def main():
    """主演示函数"""
    print("🎬 Augment续杯工具 - 功能演示")
    print("版本: v1.0.0")
    print("作者: AI Assistant")
    
    demos = [
        ("环境检测", demo_environment_detection),
        ("文件操作", demo_file_operations),
        ("使用方法", demo_cleaner_usage),
        ("工具特性", demo_features)
    ]
    
    for demo_name, demo_func in demos:
        try:
            demo_func()
        except Exception as e:
            print(f"\n❌ 演示 '{demo_name}' 出现错误: {e}")
    
    print_header("演示结束")
    print("🎉 感谢观看Augment续杯工具功能演示！")
    print("📖 更多信息请查看 README.md 文件")
    print("🚀 开始使用: python augment_cleaner_gui.py")

if __name__ == "__main__":
    main()
