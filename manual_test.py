#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动测试脚本 - 验证Augment续杯工具功能
"""

import os
import sys
import json
import sqlite3
import tempfile
import shutil
from pathlib import Path

def test_basic_imports():
    """测试基础模块导入"""
    print("🔍 测试基础模块导入...")
    try:
        import platform
        import uuid
        import random
        import string
        import time
        print("✅ 基础模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 基础模块导入失败: {e}")
        return False

def test_psutil():
    """测试psutil模块"""
    print("🔍 测试psutil模块...")
    try:
        import psutil
        print("✅ psutil模块可用")
        return True
    except ImportError:
        print("❌ psutil模块未安装")
        return False

def test_file_operations():
    """测试文件操作"""
    print("🔍 测试文件操作...")
    try:
        # 创建临时目录
        temp_dir = Path(tempfile.mkdtemp())
        print(f"   创建临时目录: {temp_dir}")
        
        # 测试JSON文件操作
        json_file = temp_dir / "test.json"
        test_data = {
            "telemetry.machineId": "old-machine-id",
            "telemetry.devDeviceId": "old-device-id",
            "other.setting": "some-value"
        }
        
        # 写入JSON
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2)
        print("   ✅ JSON写入成功")
        
        # 读取JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        print("   ✅ JSON读取成功")
        
        # 修改数据
        import uuid
        import random
        import string
        
        loaded_data["telemetry.machineId"] = str(uuid.uuid4())
        loaded_data["telemetry.devDeviceId"] = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
        
        # 写回文件
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(loaded_data, f, indent=2)
        print("   ✅ JSON修改成功")
        
        # 测试SQLite数据库操作
        db_file = temp_dir / "test.db"
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        
        # 创建表
        cursor.execute('''
            CREATE TABLE ItemTable (
                key TEXT PRIMARY KEY,
                value TEXT
            )
        ''')
        
        # 插入测试数据
        test_records = [
            ("augment.setting1", "value1"),
            ("augment.setting2", "value2"),
            ("other.setting", "other_value"),
            ("augmentcode.config", "config_value")
        ]
        
        cursor.executemany("INSERT INTO ItemTable (key, value) VALUES (?, ?)", test_records)
        conn.commit()
        print("   ✅ 数据库写入成功")
        
        # 查询数据
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]
        print(f"   清理前Augment记录数: {count_before}")
        
        # 删除Augment相关记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()
        
        # 再次查询
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]
        print(f"   清理后Augment记录数: {count_after}")
        
        conn.close()
        print("   ✅ 数据库操作成功")
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
        print("   ✅ 临时文件清理成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 文件操作失败: {e}")
        return False

def test_path_detection():
    """测试路径检测"""
    print("🔍 测试路径检测...")
    try:
        import platform
        from pathlib import Path
        
        system = platform.system()
        home = Path.home()
        
        print(f"   操作系统: {system}")
        print(f"   用户目录: {home}")
        
        if system == "Windows":
            vscode_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
        elif system == "Darwin":  # macOS
            vscode_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
        elif system == "Linux":
            vscode_path = home / ".config" / "Code" / "User" / "globalStorage"
        else:
            print(f"   ❌ 不支持的操作系统: {system}")
            return False
        
        print(f"   VS Code路径: {vscode_path}")
        print(f"   路径存在: {'✅ 是' if vscode_path.exists() else '❌ 否'}")
        
        if vscode_path.exists():
            storage_json = vscode_path / "storage.json"
            state_db = vscode_path / "state.vscdb"
            print(f"   storage.json: {'✅ 存在' if storage_json.exists() else '❌ 不存在'}")
            print(f"   state.vscdb: {'✅ 存在' if state_db.exists() else '❌ 不存在'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 路径检测失败: {e}")
        return False

def test_cleaner_class():
    """测试清理器类"""
    print("🔍 测试清理器类...")
    try:
        # 导入清理器类
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from augment_cleaner import AugmentCleaner
        
        # 创建实例
        cleaner = AugmentCleaner()
        print(f"   系统类型: {cleaner.system}")
        print(f"   备份目录: {cleaner.backup_dir}")
        
        # 检查路径
        print("   VS Code路径:")
        for name, path in cleaner.vscode_paths.items():
            status = "✅ 存在" if path.exists() else "❌ 不存在"
            print(f"     {status} {name}: {path}")
        
        # 测试ID生成
        machine_id = cleaner._generate_new_machine_id()
        device_id = cleaner._generate_new_device_id()
        print(f"   生成的机器ID: {machine_id}")
        print(f"   生成的设备ID: {device_id}")
        
        print("   ✅ 清理器类测试成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 清理器类测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 Augment续杯工具 - 手动功能测试")
    print("=" * 60)
    
    tests = [
        ("基础模块导入", test_basic_imports),
        ("psutil模块", test_psutil),
        ("文件操作", test_file_operations),
        ("路径检测", test_path_detection),
        ("清理器类", test_cleaner_class)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n【测试】{test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！Augment续杯工具可以正常使用。")
    else:
        print(f"\n⚠️ {total-passed} 个测试失败，请检查相关功能。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
