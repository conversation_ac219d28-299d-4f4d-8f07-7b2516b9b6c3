#!/bin/bash

# Augment续杯工具启动脚本
# 适用于 Linux 和 macOS

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

print_message $BLUE "========================================"
print_message $BLUE "🔧 Augment续杯工具 v1.0.0"
print_message $BLUE "========================================"
echo

# 检查Python
if ! command_exists python3; then
    print_message $RED "❌ 错误: 未检测到Python3，请先安装Python 3.6+"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macOS安装命令: brew install python3"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
    fi
    exit 1
fi

# 检查pip
if ! command_exists pip3; then
    print_message $RED "❌ 错误: 未检测到pip3"
    exit 1
fi

print_message $GREEN "✅ Python3 检查通过"

# 检查依赖
print_message $YELLOW "📦 检查依赖包..."
if ! python3 -c "import psutil" 2>/dev/null; then
    print_message $YELLOW "📥 安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        print_message $RED "❌ 依赖安装失败"
        exit 1
    fi
fi

print_message $GREEN "✅ 依赖检查完成"
echo

# 选择运行模式
echo "请选择运行模式:"
echo "[1] 图形界面版本 (推荐)"
echo "[2] 命令行版本"
echo "[3] 退出"
echo

read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        print_message $BLUE "🚀 启动图形界面版本..."
        python3 augment_cleaner_gui.py
        ;;
    2)
        print_message $BLUE "🚀 启动命令行版本..."
        python3 augment_cleaner.py
        ;;
    3)
        print_message $GREEN "👋 再见!"
        exit 0
        ;;
    *)
        print_message $RED "❌ 无效选择"
        exit 1
        ;;
esac

echo
print_message $GREEN "操作完成"
