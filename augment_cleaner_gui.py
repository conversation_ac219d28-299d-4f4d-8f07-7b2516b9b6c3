#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - GUI版本
基于tkinter的图形界面版本
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import sys
import os
from pathlib import Path

# 导入主清理模块
from augment_cleaner import AugmentCleaner

class AugmentCleanerGUI:
    """Augment续杯工具GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment续杯工具 v1.0.0")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 初始化清理器
        self.cleaner = None
        self.init_cleaner()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔧 Augment续杯工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 系统信息框架
        info_frame = ttk.LabelFrame(main_frame, text="系统信息", padding="10")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.system_label = ttk.Label(info_frame, text="正在检测系统...")
        self.system_label.grid(row=0, column=0, sticky=tk.W)
        
        # 路径信息
        self.path_text = scrolledtext.ScrolledText(info_frame, height=6, width=70)
        self.path_text.grid(row=1, column=0, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        # 检查状态按钮
        self.check_btn = ttk.Button(button_frame, text="🔍 检查状态", command=self.check_status)
        self.check_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 备份按钮
        self.backup_btn = ttk.Button(button_frame, text="💾 仅备份", command=self.backup_only)
        self.backup_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 清理按钮
        self.clean_btn = ttk.Button(button_frame, text="🧹 开始清理", command=self.start_cleaning)
        self.clean_btn.grid(row=0, column=2, padx=(0, 10))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.grid(row=4, column=0, columnspan=2)
        
        # 日志输出
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        info_frame.columnconfigure(0, weight=1)
        
    def init_cleaner(self):
        """初始化清理器"""
        try:
            self.cleaner = AugmentCleaner()
            self.update_system_info()
            self.log("✅ 清理器初始化成功")
        except Exception as e:
            self.log(f"❌ 清理器初始化失败: {e}")
            messagebox.showerror("错误", f"初始化失败: {e}")
    
    def update_system_info(self):
        """更新系统信息显示"""
        if not self.cleaner:
            return
            
        # 更新系统标签
        self.system_label.config(text=f"操作系统: {self.cleaner.system}")
        
        # 更新路径信息
        self.path_text.delete(1.0, tk.END)
        self.path_text.insert(tk.END, "VS Code配置路径:\n\n")
        
        for name, path in self.cleaner.vscode_paths.items():
            status = "✅ 存在" if path.exists() else "❌ 不存在"
            self.path_text.insert(tk.END, f"{status} {name}:\n")
            self.path_text.insert(tk.END, f"   {path}\n\n")
    
    def log(self, message):
        """添加日志信息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def set_status(self, message, style=None):
        """设置状态信息"""
        if style:
            self.status_label.config(text=message, style=f'{style}.TLabel')
        else:
            self.status_label.config(text=message)
    
    def check_status(self):
        """检查当前状态"""
        self.log("🔍 正在检查状态...")
        
        if not self.cleaner:
            self.log("❌ 清理器未初始化")
            return
        
        # 检查VS Code是否运行
        if self.cleaner.check_vscode_running():
            self.log("⚠️ 检测到VS Code正在运行")
            self.set_status("VS Code正在运行，请先关闭", "Warning")
        else:
            self.log("✅ VS Code未运行，可以执行清理")
            self.set_status("就绪，可以执行清理", "Success")
        
        # 检查文件状态
        for name, path in self.cleaner.vscode_paths.items():
            if path.exists():
                self.log(f"✅ {name}: 存在")
            else:
                self.log(f"❌ {name}: 不存在")
    
    def backup_only(self):
        """仅执行备份"""
        def backup_thread():
            try:
                self.progress.start()
                self.set_status("正在备份...", "Warning")
                
                if self.cleaner.backup_files():
                    self.log("✅ 备份完成")
                    self.set_status("备份完成", "Success")
                else:
                    self.log("❌ 备份失败")
                    self.set_status("备份失败", "Error")
                    
            except Exception as e:
                self.log(f"❌ 备份出错: {e}")
                self.set_status("备份出错", "Error")
            finally:
                self.progress.stop()
        
        threading.Thread(target=backup_thread, daemon=True).start()
    
    def start_cleaning(self):
        """开始清理流程"""
        # 确认对话框
        result = messagebox.askyesno(
            "确认清理",
            "⚠️ 此操作将:\n\n"
            "1. 备份原始文件\n"
            "2. 修改机器码和设备码\n"
            "3. 清理Augment相关数据\n\n"
            "确定要继续吗？"
        )
        
        if not result:
            return
        
        def clean_thread():
            try:
                self.progress.start()
                self.set_status("正在清理...", "Warning")
                
                # 禁用按钮
                self.clean_btn.config(state='disabled')
                self.backup_btn.config(state='disabled')
                self.check_btn.config(state='disabled')
                
                self.log("🧹 开始清理流程...")
                
                if self.cleaner.clean_all():
                    self.log("🎉 清理成功完成!")
                    self.set_status("清理完成", "Success")
                    
                    messagebox.showinfo(
                        "清理完成",
                        "✅ 清理成功完成!\n\n"
                        "后续步骤:\n"
                        "1. 重新启动VS Code\n"
                        "2. 使用新邮箱注册Augment账号\n"
                        "3. 开始使用新的免费额度"
                    )
                else:
                    self.log("❌ 清理过程中出现错误")
                    self.set_status("清理失败", "Error")
                    messagebox.showerror("清理失败", "清理过程中出现错误，请查看日志")
                    
            except Exception as e:
                self.log(f"❌ 清理出错: {e}")
                self.set_status("清理出错", "Error")
                messagebox.showerror("错误", f"清理出错: {e}")
            finally:
                self.progress.stop()
                # 重新启用按钮
                self.clean_btn.config(state='normal')
                self.backup_btn.config(state='normal')
                self.check_btn.config(state='normal')
        
        threading.Thread(target=clean_thread, daemon=True).start()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = AugmentCleanerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
