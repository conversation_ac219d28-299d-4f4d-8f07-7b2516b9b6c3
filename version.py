#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 版本信息
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__email__ = "<EMAIL>"
__description__ = "Augment续杯工具 - 自动清理VS Code中Augment插件使用痕迹"
__url__ = "https://github.com/example/augment-cleaner"

# 版本历史
VERSION_HISTORY = [
    {
        "version": "1.0.0",
        "date": "2025-01-13",
        "changes": [
            "初始版本发布",
            "支持Windows、macOS、Linux三大平台",
            "提供图形界面和命令行两种模式",
            "自动备份和恢复功能",
            "智能检测VS Code运行状态",
            "详细的操作日志记录"
        ]
    }
]

def get_version_info():
    """获取版本信息"""
    return {
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "url": __url__
    }

def print_version():
    """打印版本信息"""
    print(f"Augment续杯工具 v{__version__}")
    print(f"作者: {__author__}")
    print(f"描述: {__description__}")

if __name__ == "__main__":
    print_version()
