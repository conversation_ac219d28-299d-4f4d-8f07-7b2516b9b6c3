#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import platform
import os
from pathlib import Path

print("=" * 50)
print("🧪 Augment续杯工具 - 简单测试")
print("=" * 50)

# 测试1: Python环境
print(f"Python版本: {sys.version}")
print(f"操作系统: {platform.system()}")

# 测试2: 导入模块
try:
    import json
    import sqlite3
    import uuid
    import random
    import string
    print("✅ 基础模块导入成功")
except ImportError as e:
    print(f"❌ 基础模块导入失败: {e}")

# 测试3: 检查psutil
try:
    import psutil
    print("✅ psutil模块可用")
except ImportError:
    print("❌ psutil模块未安装")

# 测试4: 路径检测
print("\n📁 VS Code路径检测:")
home = Path.home()

if platform.system() == "Windows":
    vscode_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
elif platform.system() == "Darwin":  # macOS
    vscode_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
elif platform.system() == "Linux":
    vscode_path = home / ".config" / "Code" / "User" / "globalStorage"
else:
    vscode_path = None

if vscode_path:
    print(f"检测路径: {vscode_path}")
    print(f"路径存在: {'✅ 是' if vscode_path.exists() else '❌ 否'}")
    
    if vscode_path.exists():
        storage_json = vscode_path / "storage.json"
        state_db = vscode_path / "state.vscdb"
        print(f"storage.json: {'✅ 存在' if storage_json.exists() else '❌ 不存在'}")
        print(f"state.vscdb: {'✅ 存在' if state_db.exists() else '❌ 不存在'}")
else:
    print("❌ 不支持的操作系统")

# 测试5: 文件操作
print("\n📝 文件操作测试:")
try:
    # 创建测试目录
    test_dir = Path("./test_temp")
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试文件
    test_file = test_dir / "test.json"
    test_data = {"test": "data", "id": "12345"}
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f)
    
    # 读取测试文件
    with open(test_file, 'r', encoding='utf-8') as f:
        loaded_data = json.load(f)
    
    print("✅ JSON文件读写正常")
    
    # 清理测试文件
    test_file.unlink()
    test_dir.rmdir()
    print("✅ 文件清理正常")
    
except Exception as e:
    print(f"❌ 文件操作失败: {e}")

# 测试6: UUID生成
print("\n🔑 ID生成测试:")
try:
    import uuid
    import random
    import string
    
    machine_id = str(uuid.uuid4())
    device_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
    
    print(f"机器ID: {machine_id}")
    print(f"设备ID: {device_id}")
    print("✅ ID生成正常")
    
except Exception as e:
    print(f"❌ ID生成失败: {e}")

print("\n" + "=" * 50)
print("🎉 基础功能测试完成")
print("=" * 50)
