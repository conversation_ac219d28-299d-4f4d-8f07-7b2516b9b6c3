#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 测试脚本
用于测试工具的各项功能
"""

import os
import sys
import tempfile
import json
import sqlite3
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from augment_cleaner import AugmentCleaner

def create_test_environment():
    """创建测试环境"""
    print("🧪 创建测试环境...")
    
    # 创建临时目录
    test_dir = Path(tempfile.mkdtemp(prefix="augment_test_"))
    print(f"📁 测试目录: {test_dir}")
    
    # 创建模拟的storage.json
    storage_data = {
        "telemetry.machineId": "test-machine-id-12345",
        "telemetry.devDeviceId": "test-device-id-67890",
        "other.setting": "some-value"
    }
    
    storage_file = test_dir / "storage.json"
    with open(storage_file, 'w', encoding='utf-8') as f:
        json.dump(storage_data, f, indent=2)
    
    # 创建模拟的数据库
    db_file = test_dir / "state.vscdb"
    conn = sqlite3.connect(str(db_file))
    cursor = conn.cursor()
    
    # 创建表结构
    cursor.execute('''
        CREATE TABLE ItemTable (
            key TEXT PRIMARY KEY,
            value TEXT
        )
    ''')
    
    # 插入测试数据
    test_data = [
        ("augment.setting1", "value1"),
        ("augment.setting2", "value2"),
        ("other.setting", "other_value"),
        ("augmentcode.config", "config_value")
    ]
    
    cursor.executemany("INSERT INTO ItemTable (key, value) VALUES (?, ?)", test_data)
    conn.commit()
    conn.close()
    
    print("✅ 测试环境创建完成")
    return test_dir

def test_cleaner_functions(test_dir):
    """测试清理器功能"""
    print("\n🔧 测试清理器功能...")
    
    # 创建自定义清理器实例
    cleaner = AugmentCleaner()
    
    # 覆盖路径为测试路径
    cleaner.vscode_paths = {
        "storage_json": test_dir / "storage.json",
        "state_db": test_dir / "state.vscdb",
        "base_path": test_dir
    }
    
    print("📋 测试前状态:")
    
    # 检查storage.json
    storage_file = cleaner.vscode_paths["storage_json"]
    if storage_file.exists():
        with open(storage_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"   机器ID: {data.get('telemetry.machineId', 'N/A')}")
        print(f"   设备ID: {data.get('telemetry.devDeviceId', 'N/A')}")
    
    # 检查数据库
    db_file = cleaner.vscode_paths["state_db"]
    if db_file.exists():
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count = cursor.fetchone()[0]
        print(f"   数据库中Augment记录数: {count}")
        conn.close()
    
    # 执行备份
    print("\n💾 测试备份功能...")
    backup_success = cleaner.backup_files()
    print(f"   备份结果: {'✅ 成功' if backup_success else '❌ 失败'}")
    
    # 执行清理
    print("\n🧹 测试清理功能...")
    
    # 测试修改storage.json
    modify_success = cleaner.modify_storage_json()
    print(f"   修改storage.json: {'✅ 成功' if modify_success else '❌ 失败'}")
    
    # 测试清理数据库
    clean_success = cleaner.clean_database()
    print(f"   清理数据库: {'✅ 成功' if clean_success else '❌ 失败'}")
    
    print("\n📋 测试后状态:")
    
    # 再次检查storage.json
    if storage_file.exists():
        with open(storage_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"   机器ID: {data.get('telemetry.machineId', 'N/A')}")
        print(f"   设备ID: {data.get('telemetry.devDeviceId', 'N/A')}")
    
    # 再次检查数据库
    if db_file.exists():
        conn = sqlite3.connect(str(db_file))
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count = cursor.fetchone()[0]
        print(f"   数据库中Augment记录数: {count}")
        conn.close()
    
    return backup_success and modify_success and clean_success

def test_path_detection():
    """测试路径检测功能"""
    print("\n🔍 测试路径检测...")
    
    try:
        cleaner = AugmentCleaner()
        print(f"   检测到的系统: {cleaner.system}")
        print("   VS Code路径:")
        for name, path in cleaner.vscode_paths.items():
            status = "✅ 存在" if path.exists() else "❌ 不存在"
            print(f"     {status} {name}: {path}")
        return True
    except Exception as e:
        print(f"   ❌ 路径检测失败: {e}")
        return False

def cleanup_test_environment(test_dir):
    """清理测试环境"""
    print(f"\n🧽 清理测试环境: {test_dir}")
    try:
        import shutil
        shutil.rmtree(test_dir)
        print("✅ 测试环境清理完成")
    except Exception as e:
        print(f"⚠️ 清理测试环境失败: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 Augment续杯工具 - 功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试1: 路径检测
    print("\n【测试1】路径检测功能")
    result1 = test_path_detection()
    test_results.append(("路径检测", result1))
    
    # 测试2: 清理功能
    print("\n【测试2】清理功能测试")
    test_dir = create_test_environment()
    try:
        result2 = test_cleaner_functions(test_dir)
        test_results.append(("清理功能", result2))
    finally:
        cleanup_test_environment(test_dir)
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！工具可以正常使用。")
    else:
        print("⚠️ 部分测试失败，请检查错误信息。")
    print("=" * 50)

if __name__ == "__main__":
    main()
