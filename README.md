# 🔧 Augment续杯工具 v1.0.0

一个用于自动清理VS Code中Augment插件使用痕迹的工具，帮助用户实现"续杯"功能。

## ✨ 功能特点

- 🎯 **自动清理**: 一键清理Augment使用痕迹
- 💾 **安全备份**: 操作前自动备份原始文件
- 🖥️ **跨平台**: 支持Windows、macOS、Linux
- 🎨 **双界面**: 提供图形界面和命令行两种模式
- 🔍 **智能检测**: 自动检测VS Code运行状态
- 📝 **详细日志**: 完整的操作日志记录

## 🚀 快速开始

### 环境要求

- Python 3.6+
- VS Code (已安装Augment插件)

### 安装步骤

1. **下载工具**
   ```bash
   git clone <repository-url>
   cd augment-cleaner
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行工具**
   
   **Windows用户:**
   ```cmd
   run_cleaner.bat
   ```
   
   **Linux/macOS用户:**
   ```bash
   chmod +x run_cleaner.sh
   ./run_cleaner.sh
   ```

## 📖 使用说明

### 图形界面版本 (推荐)

1. 运行 `python augment_cleaner_gui.py`
2. 点击"🔍 检查状态"查看当前状态
3. 点击"💾 仅备份"进行备份（可选）
4. 点击"🧹 开始清理"执行完整清理流程

### 命令行版本

1. 运行 `python augment_cleaner.py`
2. 按提示确认操作
3. 等待清理完成

## 🔧 工作原理

### 清理内容

1. **机器码修改**
   - 修改 `telemetry.machineId`
   - 修改 `telemetry.devDeviceId`

2. **数据库清理**
   - 删除 `state.vscdb` 中的Augment相关记录

3. **文件路径**
   - Windows: `%APPDATA%\Code\User\globalStorage\`
   - macOS: `~/Library/Application Support/Code/User/globalStorage/`
   - Linux: `~/.config/Code/User/globalStorage/`

### 安全措施

- ✅ 操作前自动备份
- ✅ 检测VS Code运行状态
- ✅ 详细的操作日志
- ✅ 错误处理和回滚

## 📁 文件结构

```
augment-cleaner/
├── augment_cleaner.py      # 核心清理模块
├── augment_cleaner_gui.py  # 图形界面版本
├── config.json             # 配置文件
├── requirements.txt        # 依赖包列表
├── run_cleaner.bat        # Windows启动脚本
├── run_cleaner.sh         # Linux/macOS启动脚本
├── README.md              # 说明文档
└── backups/               # 备份目录
```

## ⚙️ 配置选项

编辑 `config.json` 文件可以自定义以下选项:

```json
{
  "settings": {
    "auto_backup": true,           // 自动备份
    "backup_retention_days": 7,    // 备份保留天数
    "log_level": "INFO",           // 日志级别
    "check_vscode_running": true,  // 检查VS Code运行状态
    "confirm_before_clean": true   // 清理前确认
  }
}
```

## 🔄 使用流程

1. **准备阶段**
   - 关闭VS Code
   - 运行清理工具

2. **清理阶段**
   - 自动备份原始文件
   - 修改机器码和设备码
   - 清理数据库记录

3. **完成阶段**
   - 重新启动VS Code
   - 使用新邮箱注册Augment账号
   - 开始使用新的免费额度

## ⚠️ 注意事项

1. **使用前必须关闭VS Code**
2. **建议使用新邮箱注册账号**
3. **备份文件会保存在 `backups/` 目录**
4. **如遇问题可查看日志文件**

## 🐛 故障排除

### 常见问题

**Q: 提示"VS Code正在运行"**
A: 请完全关闭VS Code后重试

**Q: 清理后仍然受限**
A: 可能需要使用新邮箱注册账号

**Q: 找不到配置文件**
A: 确认VS Code已正确安装且运行过

**Q: 权限错误**
A: 以管理员身份运行工具

### 日志查看

查看 `augment_cleaner.log` 文件获取详细错误信息。

## 📄 免责声明

本工具仅供学习和研究使用，使用者需自行承担使用风险。请遵守相关软件的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 📜 许可证

MIT License

---

**如果这个工具对您有帮助，请给个⭐Star支持一下！**
