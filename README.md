# 🔧 Augment续杯工具 v1.0.0

一个简单实用的工具，用于自动清理VS Code中Augment插件使用痕迹，帮助用户重置免费额度。

## ✨ 功能特点

- 🎯 **一键清理**: 自动清理Augment使用痕迹
- 💾 **安全备份**: 操作前自动备份原始文件
- 🖥️ **跨平台**: 支持Windows、macOS、Linux
- 🔍 **智能检测**: 自动检测VS Code运行状态

## 🚀 使用方法

### 环境要求
- Python 3.6+
- VS Code (已安装Augment插件)

### 直接运行
```bash
python augment_cleaner.py
```

### 使用步骤
1. 关闭VS Code
2. 运行工具
3. 按提示确认操作
4. 等待清理完成
5. 重启VS Code
6. 用新邮箱注册Augment账号

## 🔧 工作原理

1. **机器码修改**: 更新VS Code的设备标识
2. **数据库清理**: 删除Augment使用记录
3. **自动备份**: 操作前备份原始文件

### 支持的路径
- **Windows**: `%APPDATA%\Code\User\globalStorage\`
- **macOS**: `~/Library/Application Support/Code/User/globalStorage/`
- **Linux**: `~/.config/Code/User/globalStorage/`

## ⚠️ 注意事项

1. **使用前必须关闭VS Code**
2. **建议使用新邮箱注册账号**
3. **备份文件保存在 `backups/` 目录**

## 🐛 常见问题

**Q: 提示"VS Code正在运行"**
A: 请完全关闭VS Code后重试

**Q: 清理后仍然受限**
A: 需要使用新邮箱注册账号

**Q: 找不到配置文件**
A: 确认VS Code已安装且运行过

## 📄 免责声明

本工具仅供学习研究使用，请遵守相关软件使用条款。
