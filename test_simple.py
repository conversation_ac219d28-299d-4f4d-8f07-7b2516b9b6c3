#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import platform
from pathlib import Path

def test_basic():
    print("=" * 40)
    print("🧪 基础功能测试")
    print("=" * 40)
    
    # 测试系统检测
    system = platform.system()
    print(f"操作系统: {system}")
    
    # 测试路径检测
    home = Path.home()
    print(f"用户目录: {home}")
    
    if system == "Windows":
        vscode_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
    elif system == "Darwin":  # macOS
        vscode_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
    elif system == "Linux":
        vscode_path = home / ".config" / "Code" / "User" / "globalStorage"
    else:
        print("不支持的操作系统")
        return
    
    print(f"VS Code路径: {vscode_path}")
    print(f"路径存在: {vscode_path.exists()}")
    
    if vscode_path.exists():
        storage_json = vscode_path / "storage.json"
        state_db = vscode_path / "state.vscdb"
        print(f"storage.json: {storage_json.exists()}")
        print(f"state.vscdb: {state_db.exists()}")
    
    # 测试用户输入
    print("\n测试用户输入:")
    try:
        user_input = input("请输入任意内容: ")
        print(f"您输入了: '{user_input}'")
    except Exception as e:
        print(f"输入测试失败: {e}")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_basic()
