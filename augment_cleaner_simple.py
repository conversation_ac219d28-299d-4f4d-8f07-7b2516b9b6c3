#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 简化版
"""

import os
import sys
import json
import sqlite3
import platform
import shutil
import random
import string
import uuid
import time
from pathlib import Path

def get_vscode_paths():
    """获取VS Code配置路径"""
    system = platform.system()
    home = Path.home()
    
    if system == "Windows":
        base_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage"
    elif system == "Darwin":  # macOS
        base_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage"
    elif system == "Linux":
        base_path = home / ".config" / "Code" / "User" / "globalStorage"
    else:
        raise OSError(f"不支持的操作系统: {system}")
    
    return {
        "storage_json": base_path / "storage.json",
        "state_db": base_path / "state.vscdb",
        "base_path": base_path
    }

def backup_files(vscode_paths):
    """备份文件"""
    try:
        backup_dir = Path("./backups")
        backup_dir.mkdir(exist_ok=True)
        
        timestamp = str(int(time.time()))
        backup_folder = backup_dir / f"backup_{timestamp}"
        backup_folder.mkdir(exist_ok=True)
        
        for name, path in vscode_paths.items():
            if name != "base_path" and path.exists() and path.is_file():
                backup_path = backup_folder / f"{name}_{timestamp}"
                shutil.copy2(path, backup_path)
                print(f"✅ 已备份: {path.name}")
        
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def modify_storage_json(storage_path):
    """修改storage.json"""
    try:
        if not storage_path.exists():
            print("⚠️ storage.json不存在，跳过修改")
            return True
        
        # 读取文件
        with open(storage_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 修改机器码
        if "telemetry.machineId" in data:
            old_id = data["telemetry.machineId"]
            data["telemetry.machineId"] = str(uuid.uuid4())
            print(f"✅ 已修改机器ID")
        
        if "telemetry.devDeviceId" in data:
            old_id = data["telemetry.devDeviceId"]
            data["telemetry.devDeviceId"] = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
            print(f"✅ 已修改设备ID")
        
        # 写回文件
        with open(storage_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print("✅ storage.json修改完成")
        return True
        
    except Exception as e:
        print(f"❌ 修改storage.json失败: {e}")
        return False

def clean_database(db_path):
    """清理数据库"""
    try:
        if not db_path.exists():
            print("⚠️ 数据库不存在，跳过清理")
            return True
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 查找记录
        cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%'")
        records = cursor.fetchall()
        
        if records:
            print(f"🔍 找到 {len(records)} 条Augment记录")
            cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
            conn.commit()
            print(f"✅ 已删除 {cursor.rowcount} 条记录")
        else:
            print("✅ 未找到Augment记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 清理数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 Augment续杯工具 v1.0.0")
    print("=" * 50)
    
    try:
        # 获取路径
        vscode_paths = get_vscode_paths()
        system = platform.system()
        
        print(f"\n📍 系统: {system}")
        print("📁 VS Code配置路径:")
        for name, path in vscode_paths.items():
            if name != "base_path":
                status = "✅" if path.exists() else "❌"
                print(f"   {status} {name}: {path}")
        
        # 确认执行
        print("\n⚠️ 警告: 此操作将修改VS Code配置文件")
        print("📋 操作内容:")
        print("   1. 备份原始文件")
        print("   2. 修改机器码和设备码")
        print("   3. 清理Augment相关数据")
        
        # 获取用户确认
        while True:
            try:
                confirm = input("\n是否继续执行? (y/N): ").strip().lower()
                print(f"DEBUG: 用户输入 '{confirm}'")
                break
            except KeyboardInterrupt:
                print("\n❌ 用户取消操作")
                return
            except Exception as e:
                print(f"输入错误: {e}")
                return
        
        if confirm not in ['y', 'yes', '是']:
            print("❌ 操作已取消")
            return
        
        print("\n🚀 开始清理...")
        
        # 执行清理步骤
        print("\n💾 备份文件...")
        if not backup_files(vscode_paths):
            print("❌ 备份失败")
            return
        
        print("\n🔧 修改机器码...")
        if not modify_storage_json(vscode_paths["storage_json"]):
            print("❌ 修改失败")
            return
        
        print("\n🧹 清理数据库...")
        if not clean_database(vscode_paths["state_db"]):
            print("❌ 清理失败")
            return
        
        print("\n" + "=" * 50)
        print("🎉 清理完成!")
        print("📝 后续步骤:")
        print("   1. 重新启动VS Code")
        print("   2. 使用新邮箱注册Augment账号")
        print("   3. 开始使用新的免费额度")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
