@echo off
chcp 65001 >nul
title Augment续杯工具

echo ========================================
echo 🔧 Augment续杯工具 v1.0.0
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查依赖
echo 📦 检查依赖包...
pip show psutil >nul 2>&1
if errorlevel 1 (
    echo 📥 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成
echo.

:: 选择运行模式
echo 请选择运行模式:
echo [1] 图形界面版本 (推荐)
echo [2] 命令行版本
echo [3] 退出
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo 🚀 启动图形界面版本...
    python augment_cleaner_gui.py
) else if "%choice%"=="2" (
    echo 🚀 启动命令行版本...
    python augment_cleaner.py
) else if "%choice%"=="3" (
    echo 👋 再见!
    exit /b 0
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo 操作完成，按任意键退出...
pause >nul
