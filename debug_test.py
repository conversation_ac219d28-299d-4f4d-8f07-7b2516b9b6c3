#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试...")

try:
    print("测试1: 基础输出")
    print("Hello World")
    
    print("测试2: 用户输入")
    user_input = input("请输入y: ")
    print(f"您输入了: '{user_input}'")
    
    if user_input.lower() == 'y':
        print("✅ 输入正确，继续执行")
        print("模拟清理过程...")
        import time
        time.sleep(1)
        print("✅ 清理完成")
    else:
        print("❌ 输入不正确")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

print("测试结束")
